"""
配置管理模块
"""

import os
from typing import Dict, Any


class Config:
    """配置类"""

    # 日志系统配置
    LOG_SYSTEM_BASE_URL = "https://log.foneshare.cn"
    LOG_API_ENDPOINT = "/api/v1/instances/9/complete"
    LOG_TABLE_NAME = "`logger`.`app_log_dist`"
    LOG_TIME_FIELD = "_time_second_"
    LOG_TRACE_ID_FIELD = "traceId"

    # 请求配置
    REQUEST_TIMEOUT = 30
    MAX_RETRIES = 3

    # 默认查询配置
    DEFAULT_QUERY_LIMIT = 100
    DEFAULT_QUERY_OFFSET = 0
    MAX_QUERY_LIMIT = 1000

    # DeepSeek配置
    DEEPSEEK_API_KEY = "***********************************"
    DEEPSEEK_BASE_URL = "https://api.deepseek.com"
    DEEPSEEK_MODEL = "deepseek-chat"

    DASH_SCOPE_KEY = "***********************************"

    # 分析配置
    ANALYSIS_TEMPERATURE = 0.3
    ANALYSIS_MAX_TOKENS = 2000
    ANALYSIS_TIMEOUT = 60

    @classmethod
    def get_request_headers(cls) -> Dict[str, str]:
        """获取标准请求头"""
        return {
            "accept": "application/json, text/plain, */*",
            "accept-language": "zh-CN,zh;q=0.9",
            "cache-control": "no-cache",
            "content-length": "0",
            "origin": cls.LOG_SYSTEM_BASE_URL,
            "pragma": "no-cache",
            "priority": "u=1, i",
            "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"macOS"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
        }

    @classmethod
    def build_referer_url(
            cls,
            trace_id: str,
            start_time: int,
            end_time: int,
            page: int = 1,
            size: int = 100,
    ) -> str:
        """构建referer URL"""
        import urllib.parse

        kw = f"traceId = '{trace_id}'"
        params = {
            "end": end_time,
            "index": "3",
            "kw": kw,
            "logState": "0",
            "page": page,
            "queryType": "statisticalTable",
            "size": size,
            "start": start_time,
            "tab": "relative",
            "tid": "278",
        }

        query_string = urllib.parse.urlencode(params)
        return f"{cls.LOG_SYSTEM_BASE_URL}/query?{query_string}"
