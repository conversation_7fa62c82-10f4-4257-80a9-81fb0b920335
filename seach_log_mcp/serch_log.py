import logging

from mcp.server.fastmcp import FastMCP
from mcp.types import TextContent
from requestUtils import make_request
import math
from datetime import datetime

mcp = FastMCP("search_log")


@mcp.tool()
def get_time_stamp(time: str = None):
    """
    获取时间戳，如果用户没有明确指定开始时间和结束时间的话，则该函数返回当前时间的秒级时间戳
    如果用户传了一个时间的话，则会返回传入时间的时间戳
        args:
            time:用户传入的时间
    """
    if (time == None):
        return math.floor(datetime.now().timestamp())
    # 方法一：使用datetime模块
    # 按照字符串的格式定义格式
    format_str = "%Y-%m-%d %H:%M:%S"
    dt_obj = datetime.strptime(time, format_str)
    return math.floor(dt_obj.timestamp())


@mcp.tool()
async def search_app_log(sql: str) -> list[TextContent]:
    """
       根据sql查询日志，返回日志列表
       Args:
           sql: 查询sql
       Returns:
           list: 日志列表
       """
    params = {
        "query": sql,
    }
    result = await make_request(
        url="https://log.foneshare.cn/api/v1/instances/9/complete",
        params=params,
        method="POST",
    )
    logging.log(f"info result:{result}")
    if result["success"]:
        return result["data"]
    else:
        return []


@mcp.tool()
def build_sql(
        traceId=None,
        start_time=None,
        end_time=None,
        level=None,
        table_name="app_log_dist",
        objectId=None,
        msg=None,
) -> str:
    """
    根据traceId, start_time, end_time,level构建sql
    Args:
        traceId: 追踪id
        start_time: 开始时间 秒级的时间戳
        end_time: 结束时间 秒级的时间戳 用户没有特殊指定则为当前时间
        level: 日志级别 可选值：INFO、WARN、ERROR、DEBUG
        objectId: 数据id
        msg: 日志内容 一般用作模糊查询：
        table_name: 表名
            相关表的表名及作用:
                `logger`.`app_log_dist` 所有日志信息表
                `logger`.`log_error_dist` 错误日志信息使用该表
                `logger`.`tomcat_access_dist` tomcat访问日志表 用于查询服务间调用信息
                `logger`.`paas_metadata_changes_dist` 数据变更记录表，用于查询数据的变更情况
    """
    # 构建基础SQL查询语句
    sql_parts = [f"SELECT * FROM logger.{table_name}"]

    # 构建WHERE条件列表
    where_conditions = []

    # 添加traceId条件
    if traceId:
        where_conditions.append(f"traceId = '{traceId}'")

    # 添加时间范围条件
    if start_time:
        where_conditions.append(f"_time_second_ >= {start_time}")

    if end_time:
        where_conditions.append(f"_time_second_ <= {end_time}")

    # 添加日志级别条件
    if level:
        where_conditions.append(f"level = '{level}'")

    # 添加objectId条件
    if objectId:
        where_conditions.append(f"objectId = '{objectId}'")

    if msg:
        where_conditions.append(f"msg LIKE '%{msg}%'")

    # 如果有WHERE条件，则拼接到SQL中
    if where_conditions:
        sql_parts.append("WHERE")
        sql_parts.append(" AND ".join(where_conditions))

    # 添加排序条件，按时间倒序
    sql_parts.append("ORDER BY _time_second_ DESC")

    # 添加限制条数，避免返回过多数据
    sql_parts.append("LIMIT 100")

    # 拼接完整SQL语句
    sql = " ".join(sql_parts)

    return sql


if __name__ == "__main__":
    # 初始化并运行 FastMCP 服务器，使用标准输入输出作为传输方式
    mcp.run(transport='stdio')
    # print(get_time_stamp())
