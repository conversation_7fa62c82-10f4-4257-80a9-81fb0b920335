"""
请求工具模块
"""

import aiohttp
import asyncio
import json
import logging
from typing import Dict, Any, Optional, Union
from urllib.parse import urlencode

logger = logging.getLogger(__name__)


async def make_request(
    url: str,
    params: Optional[Dict[str, Any]] = None,
    method: str = "GET",
    headers: Optional[Dict[str, str]] = None,
    timeout: int = 30,
    **kwargs,
) -> Dict[str, Any]:
    """
    通用HTTP请求函数

    Args:
        url: 请求URL
        params: 请求参数字典
        method: HTTP方法，默认GET
        headers: 请求头字典
        timeout: 超时时间（秒），默认30秒
        **kwargs: 其他aiohttp参数

    Returns:
        Dict[str, Any]: 包含响应数据的字典，格式为：
        {
            "success": bool,  # 请求是否成功
            "status_code": int,  # HTTP状态码
            "data": Any,  # 响应数据
            "error_message": str,  # 错误信息（如果有）
            "headers": dict  # 响应头
        }
    """
    try:
        # 设置默认请求头
        default_headers = {
            "Content-Type": "application/json",
            "User-Agent": "LogAnalysisAgent/1.0.0",
        }

        if headers:
            default_headers.update(headers)

        # 创建超时配置
        client_timeout = aiohttp.ClientTimeout(total=timeout)

        # 创建HTTP会话
        async with aiohttp.ClientSession(
            timeout=client_timeout, headers=default_headers
        ) as session:

            # 根据HTTP方法发送请求
            if method.upper() == "GET":
                # GET请求将参数添加到URL
                if params:
                    separator = "&" if "?" in url else "?"
                    url = f"{url}{separator}{urlencode(params)}"

                async with session.get(url, **kwargs) as response:
                    return await _process_response(response)

            elif method.upper() == "POST":
                # POST请求将参数作为JSON数据发送
                json_data = params if params else {}

                async with session.post(url, json=json_data, **kwargs) as response:
                    return await _process_response(response)

            elif method.upper() == "PUT":
                # PUT请求将参数作为JSON数据发送
                json_data = params if params else {}

                async with session.put(url, json=json_data, **kwargs) as response:
                    return await _process_response(response)

            elif method.upper() == "DELETE":
                # DELETE请求
                if params:
                    separator = "&" if "?" in url else "?"
                    url = f"{url}{separator}{urlencode(params)}"

                async with session.delete(url, **kwargs) as response:
                    return await _process_response(response)

            else:
                return {
                    "success": False,
                    "status_code": 0,
                    "data": None,
                    "error_message": f"不支持的HTTP方法: {method}",
                    "headers": {},
                }

    except asyncio.TimeoutError:
        logger.error(f"请求超时: {url}")
        return {
            "success": False,
            "status_code": 0,
            "data": None,
            "error_message": f"请求超时（{timeout}秒）",
            "headers": {},
        }

    except aiohttp.ClientError as e:
        logger.error(f"客户端错误: {url} - {str(e)}")
        return {
            "success": False,
            "status_code": 0,
            "data": None,
            "error_message": f"客户端错误: {str(e)}",
            "headers": {},
        }

    except Exception as e:
        logger.error(f"请求异常: {url} - {str(e)}")
        return {
            "success": False,
            "status_code": 0,
            "data": None,
            "error_message": f"请求异常: {str(e)}",
            "headers": {},
        }


async def _process_response(response: aiohttp.ClientResponse) -> Dict[str, Any]:
    """
    处理HTTP响应

    Args:
        response: aiohttp响应对象

    Returns:
        Dict[str, Any]: 处理后的响应数据
    """
    try:
        # 获取响应头
        response_headers = dict(response.headers)

        # 获取响应文本
        response_text = await response.text()

        # 尝试解析JSON
        try:
            response_data = json.loads(response_text) if response_text else {}
        except json.JSONDecodeError:
            # 如果不是JSON格式，直接返回文本
            response_data = response_text

        # 判断请求是否成功
        success = 200 <= response.status < 300

        result = {
            "success": success,
            "status_code": response.status,
            "data": response_data,
            "error_message": None if success else f"HTTP {response.status}",
            "headers": response_headers,
        }

        if success:
            logger.info(f"请求成功: {response.status}")
        else:
            logger.warning(f"请求失败: {response.status} - {response_text}")

        return result

    except Exception as e:
        logger.error(f"处理响应失败: {str(e)}")
        return {
            "success": False,
            "status_code": response.status,
            "data": None,
            "error_message": f"处理响应失败: {str(e)}",
            "headers": {},
        }


def make_sync_request(
    url: str,
    params: Optional[Dict[str, Any]] = None,
    method: str = "GET",
    headers: Optional[Dict[str, str]] = None,
    timeout: int = 30,
    **kwargs,
) -> Dict[str, Any]:
    """
    同步版本的HTTP请求函数

    Args:
        url: 请求URL
        params: 请求参数字典
        method: HTTP方法，默认GET
        headers: 请求头字典
        timeout: 超时时间（秒），默认30秒
        **kwargs: 其他参数

    Returns:
        Dict[str, Any]: 响应数据字典
    """
    try:
        # 获取或创建事件循环
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        # 运行异步请求
        return loop.run_until_complete(
            make_request(url, params, method, headers, timeout, **kwargs)
        )

    except Exception as e:
        logger.error(f"同步请求失败: {url} - {str(e)}")
        return {
            "success": False,
            "status_code": 0,
            "data": None,
            "error_message": f"同步请求失败: {str(e)}",
            "headers": {},
        }
