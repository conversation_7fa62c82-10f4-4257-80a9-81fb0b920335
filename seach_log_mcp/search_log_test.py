from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
import asyncio

# 为 stdio 连接创建服务器参数
server_params = StdioServerParameters(
    # 服务器执行的命令，这里是 python
    command="python",
    # 启动命令的附加参数，这里是运行 example_server.py
    args=["serch_log.py"],
    # 环境变量，默认为 None，表示使用当前环境变量
    env=None
)



async def run():
    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()
            tools =await session.list_tools()
            return tools


if __name__ == '__main__':
    print(asyncio.run(run()))
