import logging
import json
from typing import Optional, Union

from mcp.server.fastmcp import FastMCP
from mcp.types import TextContent
from .requestUtils import make_request
import math
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

mcp = FastMCP("search_log")


@mcp.tool()
def get_time_stamp(time: Optional[str] = None) -> int:
    """
    获取时间戳，如果用户没有明确指定开始时间和结束时间的话，则该函数返回当前时间的秒级时间戳
    如果用户传了一个时间的话，则会返回传入时间的时间戳
    Args:
        time: 用户传入的时间，格式为 "YYYY-MM-DD HH:MM:SS"
    Returns:
        int: 秒级时间戳
    """
    try:
        if time is None:
            return math.floor(datetime.now().timestamp())

        # 验证时间格式
        format_str = "%Y-%m-%d %H:%M:%S"
        dt_obj = datetime.strptime(time, format_str)
        return math.floor(dt_obj.timestamp())
    except ValueError as e:
        logging.error(f"时间格式错误: {time}, 错误: {str(e)}")
        # 返回当前时间戳作为默认值
        return math.floor(datetime.now().timestamp())


@mcp.tool()
async def search_app_log(sql: str) -> list[TextContent]:
    """
    根据sql查询日志，返回日志列表
    Args:
        sql: 查询sql
    Returns:
        list[TextContent]: 包装后的日志列表
    """
    try:
        params = {
            "query": sql,
        }
        result = await make_request(
            url="https://log.foneshare.cn/api/v1/instances/9/complete",
            params=params,
            method="POST",
        )
        logging.info(f"API请求结果: {result}")

        if result["success"] and result["data"]:
            # 将结果包装成 TextContent 对象
            log_data = result["data"]
            if isinstance(log_data, list):
                # 如果是列表，格式化每条日志
                formatted_logs = []
                for i, log_entry in enumerate(log_data):
                    formatted_log = json.dumps(log_entry, ensure_ascii=False, indent=2)
                    formatted_logs.append(f"日志条目 {i+1}:\n{formatted_log}")
                content = "\n" + "=" * 50 + "\n".join(formatted_logs)
            else:
                # 如果是其他格式，直接转换
                content = json.dumps(log_data, ensure_ascii=False, indent=2)

            return [TextContent(type="text", text=content)]
        else:
            error_msg = result.get("error_message", "未知错误")
            return [TextContent(type="text", text=f"查询失败: {error_msg}")]

    except Exception as e:
        logging.error(f"查询日志时发生错误: {str(e)}")
        return [TextContent(type="text", text=f"查询出错: {str(e)}")]


@mcp.tool()
def build_sql(
    traceId: Optional[str] = None,
    start_time: Optional[int] = None,
    end_time: Optional[int] = None,
    level: Optional[str] = None,
    table_name: str = "app_log_dist",
    objectId: Optional[str] = None,
    msg: Optional[str] = None,
) -> str:
    """
    根据traceId, start_time, end_time,level构建sql
    Args:
        traceId: 追踪id
        start_time: 开始时间 秒级的时间戳
        end_time: 结束时间 秒级的时间戳 用户没有特殊指定则为当前时间
        level: 日志级别 可选值：INFO、WARN、ERROR、DEBUG
        objectId: 数据id
        msg: 日志内容 一般用作模糊查询：
        table_name: 表名
            相关表的表名及作用:
                `logger`.`app_log_dist` 所有日志信息表
                `logger`.`log_error_dist` 错误日志信息使用该表
                `logger`.`tomcat_access_dist` tomcat访问日志表 用于查询服务间调用信息
                `logger`.`paas_metadata_changes_dist` 数据变更记录表，用于查询数据的变更情况
    """
    try:
        # 参数验证
        valid_tables = [
            "app_log_dist",
            "log_error_dist",
            "tomcat_access_dist",
            "paas_metadata_changes_dist",
        ]
        if table_name not in valid_tables:
            logging.warning(f"无效的表名: {table_name}, 使用默认表名")
            table_name = "app_log_dist"

        valid_levels = ["INFO", "WARN", "ERROR", "DEBUG"]
        if level and level not in valid_levels:
            logging.warning(f"无效的日志级别: {level}, 忽略该条件")
            level = None

        # 构建基础SQL查询语句
        sql_parts = [f"SELECT * FROM logger.{table_name}"]

        # 构建WHERE条件列表
        where_conditions = []

        # 添加traceId条件（简单的字符串清理）
        if traceId:
            # 移除潜在的危险字符
            clean_trace_id = (
                str(traceId).replace("'", "").replace(";", "").replace("--", "")
            )
            where_conditions.append(f"traceId = '{clean_trace_id}'")

        # 添加时间范围条件
        if start_time is not None:
            if isinstance(start_time, (int, float)) and start_time > 0:
                where_conditions.append(f"_time_second_ >= {int(start_time)}")

        if end_time is not None:
            if isinstance(end_time, (int, float)) and end_time > 0:
                where_conditions.append(f"_time_second_ <= {int(end_time)}")

        # 添加日志级别条件
        if level:
            where_conditions.append(f"level = '{level}'")

        # 添加objectId条件
        if objectId:
            clean_object_id = (
                str(objectId).replace("'", "").replace(";", "").replace("--", "")
            )
            where_conditions.append(f"objectId = '{clean_object_id}'")

        # 添加消息模糊查询条件
        if msg:
            clean_msg = str(msg).replace("'", "''")  # SQL转义单引号
            where_conditions.append(f"msg LIKE '%{clean_msg}%'")

        # 如果有WHERE条件，则拼接到SQL中
        if where_conditions:
            sql_parts.append("WHERE")
            sql_parts.append(" AND ".join(where_conditions))

        # 添加排序条件，按时间倒序
        sql_parts.append("ORDER BY _time_second_ DESC")

        # 添加限制条数，避免返回过多数据
        sql_parts.append("LIMIT 100")

        # 拼接完整SQL语句
        sql = " ".join(sql_parts)

        logging.info(f"构建的SQL: {sql}")
        return sql

    except Exception as e:
        logging.error(f"构建SQL时发生错误: {str(e)}")
        # 返回一个基本的查询作为默认值
        return (
            f"SELECT * FROM logger.{table_name} ORDER BY _time_second_ DESC LIMIT 100"
        )


if __name__ == "__main__":
    # 初始化并运行 FastMCP 服务器，使用标准输入输出作为传输方式
    mcp.run(transport="stdio")
    # print(get_time_stamp())
