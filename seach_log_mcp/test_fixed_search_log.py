#!/usr/bin/env python3
"""
测试修复后的 search_log.py 文件
"""

import sys
import os
import asyncio

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(__file__))

# 导入修复后的模块
try:
    from search_log import get_time_stamp, build_sql
    print("✅ 成功导入修复后的模块")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)


def test_get_time_stamp():
    """测试时间戳函数"""
    print("\n🧪 测试 get_time_stamp 函数:")
    
    # 测试无参数调用
    current_timestamp = get_time_stamp()
    print(f"  当前时间戳: {current_timestamp}")
    
    # 测试有效时间格式
    test_time = "2025-07-25 12:00:00"
    timestamp = get_time_stamp(test_time)
    print(f"  {test_time} 的时间戳: {timestamp}")
    
    # 测试无效时间格式
    invalid_time = "invalid-time"
    timestamp = get_time_stamp(invalid_time)
    print(f"  无效时间 '{invalid_time}' 返回默认时间戳: {timestamp}")


def test_build_sql():
    """测试SQL构建函数"""
    print("\n🧪 测试 build_sql 函数:")
    
    # 测试基本SQL构建
    sql1 = build_sql()
    print(f"  基本SQL: {sql1}")
    
    # 测试带参数的SQL构建
    sql2 = build_sql(
        traceId="test-trace-123",
        start_time=1721900000,
        end_time=1721903600,
        level="ERROR"
    )
    print(f"  带参数SQL: {sql2}")
    
    # 测试无效参数处理
    sql3 = build_sql(
        level="INVALID_LEVEL",
        table_name="invalid_table"
    )
    print(f"  无效参数SQL: {sql3}")


def main():
    """主测试函数"""
    print("🚀 开始测试修复后的 search_log.py")
    
    test_get_time_stamp()
    test_build_sql()
    
    print("\n✅ 所有测试完成！")
    print("\n📝 修复总结:")
    print("  1. ✅ 修复了导入路径问题")
    print("  2. ✅ 添加了完整的类型注解")
    print("  3. ✅ 修复了返回类型不匹配问题")
    print("  4. ✅ 添加了错误处理和参数验证")
    print("  5. ✅ 配置了日志系统")
    print("  6. ✅ 提高了SQL构建的安全性")


if __name__ == "__main__":
    main()
