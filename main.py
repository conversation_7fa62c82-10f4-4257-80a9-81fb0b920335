from langchain_core.messages import ToolMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate, HumanMessagePromptTemplate
from langchain_deepseek import ChatDeepSeek

from config import Config
from tools.tools import build_sql, search_log, get_time_stamp

llm = ChatDeepSeek(model="deepseek-chat", api_key=Config.DEEPSEEK_API_KEY)
# llm = Tongyi(api_key=Config.DASH_SCOPE_KEY,model="tongyi")
tool_llm = llm.bind_tools([build_sql, search_log, get_time_stamp])
# query = "查一下这个trace，最近30分钟的日志 fs-file-server/68748b57388465000628a319"
prompt_template = ChatPromptTemplate.from_messages([
    SystemMessage(
        content="你是一个java服务的日志分析大师，你面对的用户是一个java初级程序员"
                "你善于根据用户给出的条件，从众多繁杂的日志中查到关键的信息，并分析本次事件中存在的问题和异常"
                "用清晰的大白话，告知用户问题原因并给出解决方案"),
    HumanMessagePromptTemplate.from_template(
        "{query}"
    )
])


def build_tool_call_message(name, id, result):
    return ToolMessage(
        name=name,
        tool_call_id=id,
        content=result
    )


def execute_search(response, messages: list):
    while response.tool_calls is not None and len(response.tool_calls) > 0:
        tool_calls = response.tool_calls
        for tool_call in tool_calls:
            tool_name = tool_call["name"]
            if tool_name == "build_sql":
                result = build_sql.invoke(tool_call["args"])
            elif tool_name == "search_log":
                result = search_log.invoke(tool_call["args"])
            elif tool_name == "get_time_stamp":
                result = get_time_stamp.invoke(tool_call["args"])
            else:
                break
            tool_call_message = build_tool_call_message(name=tool_name, id=tool_call["id"], result=result)
        print("=====工具执行=======")
        print(tool_call_message)
        print("=====工具执行结束=======")
        messages.append(tool_call_message)
        response = tool_llm.invoke(messages)
        messages.append(response)
    return response.content


def main(query):
    print(query)
    messages = prompt_template.format_prompt(query=query).to_messages()
    print(messages)
    response = tool_llm.invoke(messages)
    print(response)
    messages.append(response)
    print("*******************模型回复结果***************************")
    print(execute_search(response=response, messages=messages))


if __name__ == "__main__":
    main("查一下这个trace的问题:E-E.hbajjxsb.1337-97600382,时间在2025-07-25 11:08:54左右")
    # sql = """SELECT
    #   *
    # FROM
    #   `logger`.`app_log_dist`
    # WHERE
    #   _time_second_ >= toDateTime64(1753394400, 3)
    #   AND _time_second_ < toDateTime64(1753416000, 3)
    #   AND (traceId = 'E-E.hbajjxsb.1337-97600382')
    # ORDER BY
    #   _time_second_ DESC
    # LIMIT
    #   100
    # OFFSET
    #   0"""
    # print(search_log(sql))
